const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { getMigratorDocPath, getMigratorDocContent, getAllMigratorDocs } = require('./migrator-docs');

/**
 * 迁移文档生成器
 * 根据 package.json 分析结果和 migrator/docs 下的组件文档生成详细的迁移指导文档
 */
class MigrationDocGenerator {
  constructor(projectPath, analysisResult, options = {}) {
    this.projectPath = projectPath;
    this.analysisResult = analysisResult || {};
    this.options = {
      outputPath: options.outputPath || path.join(projectPath, 'migration-report.md'),
      includeUsageDetails: options.includeUsageDetails !== false,
      language: options.language || 'zh-CN',
      ...options
    };

    // 如果没有分析结果，从 package.json 生成
    if (!this.analysisResult.dependencies) {
      this.analysisResult = this.generateAnalysisFromPackageJson();
    }
  }

  /**
   * 从 package.json 生成分析结果
   */
  generateAnalysisFromPackageJson() {
    try {
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!fs.existsSync(packageJsonPath)) {
        return { dependencies: [], summary: { totalDependencies: 0, dependenciesWithDocs: 0, dependenciesWithoutDocs: 0 } };
      }

      const packageJson = fs.readJsonSync(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const dependencies = [];
      let dependenciesWithDocs = 0;

      for (const [name, version] of Object.entries(allDeps)) {
        const hasDoc = !!getMigratorDocPath(name);
        if (hasDoc) dependenciesWithDocs++;

        dependencies.push({
          name,
          version,
          hasDoc,
          category: this.categorizeDependency(name)
        });
      }

      return {
        dependencies,
        summary: {
          totalDependencies: dependencies.length,
          dependenciesWithDocs,
          dependenciesWithoutDocs: dependencies.length - dependenciesWithDocs,
          filesScanned: 0,
          usageFound: 0
        },
        usageInCode: []
      };
    } catch (error) {
      console.warn(chalk.yellow('⚠️  读取 package.json 失败:'), error.message);
      return { dependencies: [], summary: { totalDependencies: 0, dependenciesWithDocs: 0, dependenciesWithoutDocs: 0 }, usageInCode: [] };
    }
  }

  /**
   * 分类依赖
   */
  categorizeDependency(name) {
    if (name.includes('vue') && (name.includes('router') || name.includes('vuex'))) {
      return 'vue-core';
    }
    if (name.includes('element') || name.includes('ant-design') || name.includes('vuetify')) {
      return 'ui-library';
    }
    if (name.includes('chart') || name.includes('echarts') || name.includes('d3')) {
      return 'chart';
    }
    if (name.includes('editor') || name.includes('tinymce') || name.includes('codemirror')) {
      return 'editor';
    }
    if (name.includes('vue-') || name.includes('@vue/')) {
      return 'utility';
    }
    return 'other';
  }

  /**
   * 生成迁移指导文档
   */
  async generateMigrationGuide() {
    try {
      const content = this.buildDocumentContent();
      
      await fs.writeFile(this.options.outputPath, content, 'utf8');
      
      return {
        success: true,
        outputPath: this.options.outputPath,
        contentLength: content.length
      };
    } catch (error) {
      console.error(chalk.red('❌ 生成迁移文档失败:'), error.message);
      throw error;
    }
  }

  /**
   * 构建文档内容
   */
  buildDocumentContent() {
    const sections = [
      this.buildHeader(),
      this.buildSummary(),
      this.buildAvailableMigrationDocs(),
      this.buildDependencyMigrationGuide(),
      this.buildCodeMigrationGuide(),
      this.buildStepByStepGuide(),
      this.buildTroubleshooting(),
      this.buildResources(),
      this.buildFooter()
    ];

    return sections.join('\n\n');
  }

  /**
   * 构建可用迁移文档概览
   */
  buildAvailableMigrationDocs() {
    const allDocs = getAllMigratorDocs();

    if (allDocs.length === 0) {
      return `## 📚 可用迁移文档

暂无可用的迁移文档。`;
    }

    let section = `## 📚 可用迁移文档

本工具包含以下组件的详细迁移指导文档：

`;

    allDocs.forEach(doc => {
      const docContent = getMigratorDocContent(doc.name);
      let title = doc.name;

      // 尝试从文档内容中提取标题
      if (docContent) {
        const titleMatch = docContent.match(/^---\s*\ntitle:\s*['"]?([^'"]+)['"]?\s*\n/m);
        if (titleMatch) {
          title = titleMatch[1];
        }
      }

      section += `- **${doc.name}**: ${title}\n`;
    });

    section += `\n总计 ${allDocs.length} 个组件的迁移文档可用。`;

    return section;
  }

  /**
   * 构建文档头部
   */
  buildHeader() {
    const projectName = path.basename(this.projectPath);
    const timestamp = new Date().toLocaleString('zh-CN');

    return `# Vue 2 到 Vue 3 迁移指导文档

> 项目: ${projectName}  
> 生成时间: ${timestamp}  
> 工具版本: vue-migrator v1.0.0

本文档基于项目分析结果自动生成，提供详细的 Vue 2 到 Vue 3 迁移指导。

## 📋 目录

- [迁移概览](#迁移概览)
- [依赖迁移指南](#依赖迁移指南)
- [代码迁移指南](#代码迁移指南)
- [分步迁移指南](#分步迁移指南)
- [常见问题](#常见问题)
- [参考资源](#参考资源)`;
  }

  /**
   * 构建迁移概览
   */
  buildSummary() {
    const { summary } = this.analysisResult;

    return `## 📊 迁移概览

### 分析结果

- **总依赖数**: ${summary.totalDependencies}
- **有迁移文档**: ${summary.dependenciesWithDocs}
- **无迁移文档**: ${summary.dependenciesWithoutDocs}
- **扫描文件数**: ${summary.filesScanned}
- **发现使用**: ${summary.usageFound} 处

### 迁移复杂度评估

${this.assessMigrationComplexity()}

### 预估时间

${this.estimateMigrationTime()}`;
  }

  /**
   * 评估迁移复杂度
   */
  assessMigrationComplexity() {
    const { summary } = this.analysisResult;
    
    let complexity = '简单';
    let description = '大部分依赖都有明确的迁移路径';

    if (summary.dependenciesWithoutDocs > 5) {
      complexity = '复杂';
      description = '存在较多没有迁移文档的依赖，需要手动研究迁移方案';
    } else if (summary.dependenciesWithoutDocs > 2) {
      complexity = '中等';
      description = '部分依赖需要手动处理，但整体可控';
    }

    return `**复杂度**: ${complexity}

${description}`;
  }

  /**
   * 估算迁移时间
   */
  estimateMigrationTime() {
    const { summary } = this.analysisResult;
    
    // 简单的时间估算逻辑
    let hours = 2; // 基础时间
    hours += summary.totalDependencies * 0.5; // 每个依赖 0.5 小时
    hours += summary.dependenciesWithoutDocs * 2; // 无文档依赖额外 2 小时
    hours += Math.ceil(summary.usageFound / 10) * 1; // 每 10 处使用 1 小时

    const days = Math.ceil(hours / 8);

    return `**预估时间**: ${hours} 小时 (约 ${days} 个工作日)

*注意: 实际时间可能因项目复杂度和团队经验而有所不同*`;
  }

  /**
   * 构建依赖迁移指南
   */
  buildDependencyMigrationGuide() {
    let content = `## 📦 依赖迁移指南

### 需要迁移的依赖

以下是检测到的需要迁移的依赖及其处理方案：

`;

    // 按类别分组依赖
    const dependenciesByCategory = this.groupDependenciesByCategory();

    for (const [category, deps] of Object.entries(dependenciesByCategory)) {
      content += `#### ${this.getCategoryDisplayName(category)}\n\n`;
      
      for (const dep of deps) {
        content += this.buildDependencyMigrationSection(dep);
      }
      
      content += '\n';
    }

    return content;
  }

  /**
   * 按类别分组依赖
   */
  groupDependenciesByCategory() {
    const groups = {};
    
    for (const dep of this.analysisResult.dependencies) {
      if (!groups[dep.category]) {
        groups[dep.category] = [];
      }
      groups[dep.category].push(dep);
    }
    
    return groups;
  }

  /**
   * 获取类别显示名称
   */
  getCategoryDisplayName(category) {
    const names = {
      'vue-core': '🔧 Vue 核心',
      'ui-library': '🎨 UI 组件库',
      'chart': '📊 图表组件',
      'editor': '✏️ 编辑器组件',
      'utility': '🛠️ 工具组件',
      'other': '📋 其他组件'
    };
    
    return names[category] || '📋 其他组件';
  }

  /**
   * 构建单个依赖的迁移部分
   */
  buildDependencyMigrationSection(dependency) {
    const hasDoc = dependency.hasDoc;
    const usageLocations = this.getUsageLocations(dependency.name);

    let section = `##### ${dependency.name}\n\n`;
    section += `- **当前版本**: ${dependency.version}\n`;
    section += `- **迁移文档**: ${hasDoc ? '✅ 有' : '❌ 无'}\n`;
    section += `- **使用位置**: ${usageLocations.length} 处\n\n`;

    if (hasDoc) {
      // 读取并包含迁移文档内容
      const docContent = getMigratorDocContent(dependency.name);
      if (docContent) {
        section += `**迁移指导**:\n\n`;
        section += `${docContent}\n\n`;
        section += `---\n\n`;
      } else {
        section += `**迁移指导**: 请参考 \`migrate-cli/src/migrator/docs/${dependency.name}.md\`\n\n`;
      }
    } else {
      section += this.generateGenericMigrationAdvice(dependency);
    }

    if (this.options.includeUsageDetails && usageLocations.length > 0) {
      section += `**使用详情**:\n`;
      usageLocations.forEach(location => {
        section += `- \`${location.file}:${location.line}\` - ${location.content}\n`;
      });
      section += '\n';
    }

    return section;
  }

  /**
   * 生成通用迁移建议
   */
  generateGenericMigrationAdvice(dependency) {
    let advice = `**建议操作**:\n`;
    
    switch (dependency.category) {
      case 'vue-core':
        advice += `1. 检查 [Vue 3 官方迁移指南](https://v3-migration.vuejs.org/)\n`;
        advice += `2. 更新到 Vue 3 兼容版本\n`;
        advice += `3. 使用 Vue 3 迁移构建版本进行渐进式迁移\n`;
        break;
      case 'ui-library':
        advice += `1. 查找该组件库的 Vue 3 版本\n`;
        advice += `2. 如果没有 Vue 3 版本，寻找替代方案\n`;
        advice += `3. 更新组件的导入和使用方式\n`;
        break;
      default:
        advice += `1. 访问 [${dependency.name} 官方文档](https://www.npmjs.com/package/${dependency.name})\n`;
        advice += `2. 检查是否支持 Vue 3\n`;
        advice += `3. 如不支持，寻找 Vue 3 兼容的替代方案\n`;
    }
    
    advice += '\n';
    return advice;
  }

  /**
   * 构建代码迁移指南
   */
  buildCodeMigrationGuide() {
    return `## 💻 代码迁移指南

### 通用迁移原则

1. **渐进式迁移**: 建议使用 Vue 3 的兼容构建版本进行渐进式迁移
2. **组件优先**: 先迁移叶子组件，再迁移父组件
3. **测试驱动**: 每迁移一个组件都要进行充分测试

### 主要变更点

#### 1. 组件定义方式
\`\`\`javascript
// Vue 2
export default {
  name: 'MyComponent',
  // ...
}

// Vue 3 (推荐)
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'MyComponent',
  // ...
})
\`\`\`

#### 2. 生命周期钩子
\`\`\`javascript
// Vue 2
beforeDestroy() { },
destroyed() { }

// Vue 3
beforeUnmount() { },
unmounted() { }
\`\`\`

#### 3. 事件处理
\`\`\`javascript
// Vue 2
this.$on('event', handler)
this.$off('event', handler)
this.$emit('event', data)

// Vue 3
// 使用 mitt 或其他事件库替代 $on/$off
this.$emit('event', data) // 仍然可用
\`\`\`

### 文件修改建议

${this.buildFileModificationSuggestions()}`;
  }

  /**
   * 构建文件修改建议
   */
  buildFileModificationSuggestions() {
    const fileGroups = this.groupUsageByFile();
    
    if (Object.keys(fileGroups).length === 0) {
      return '暂未发现需要特别关注的文件。';
    }

    let suggestions = '';
    
    for (const [filePath, usages] of Object.entries(fileGroups)) {
      suggestions += `#### ${filePath}\n\n`;
      suggestions += `发现 ${usages.length} 处需要关注的使用：\n\n`;
      
      usages.forEach(usage => {
        suggestions += `- 第 ${usage.line} 行: \`${usage.content}\`\n`;
        suggestions += `  - 依赖: ${usage.dependency}\n`;
        suggestions += `  - 类型: ${usage.type}\n\n`;
      });
    }
    
    return suggestions;
  }

  /**
   * 按文件分组使用情况
   */
  groupUsageByFile() {
    const groups = {};
    
    for (const usage of this.analysisResult.usageInCode) {
      if (!groups[usage.file]) {
        groups[usage.file] = [];
      }
      groups[usage.file].push(usage);
    }
    
    return groups;
  }

  /**
   * 获取依赖的使用位置
   */
  getUsageLocations(dependencyName) {
    return this.analysisResult.usageInCode.filter(usage => 
      usage.dependency === dependencyName
    );
  }

  /**
   * 构建分步迁移指南
   */
  buildStepByStepGuide() {
    return `## 🚀 分步迁移指南

### 阶段一：准备工作 (1-2 天)

1. **项目备份**
   \`\`\`bash
   git checkout -b vue3-migration
   git commit -am "开始 Vue 3 迁移"
   \`\`\`

2. **依赖分析**
   - 使用本工具分析项目依赖
   - 制定详细的迁移计划
   - 准备测试环境

### 阶段二：核心依赖迁移 (2-3 天)

1. **Vue 核心升级**
   \`\`\`bash
   npm install vue@next @vue/compat
   npm install vue-router@4 vuex@4  # 或 pinia
   \`\`\`

2. **构建工具配置**
   - 更新 webpack/vite 配置
   - 配置 Vue 3 兼容模式

### 阶段三：组件库迁移 (3-5 天)

1. **UI 组件库**
   - Element UI → Element Plus
   - 其他 UI 库的 Vue 3 版本

2. **自定义组件**
   - 逐个迁移自定义组件
   - 更新组件的使用方式

### 阶段四：业务代码迁移 (5-10 天)

1. **页面组件迁移**
   - 从叶子组件开始
   - 逐步向上迁移

2. **状态管理迁移**
   - Vuex 4 或迁移到 Pinia
   - 更新状态管理逻辑

### 阶段五：测试和优化 (2-3 天)

1. **功能测试**
   - 全面测试所有功能
   - 修复发现的问题

2. **性能优化**
   - 利用 Vue 3 的性能优势
   - 优化组件结构`;
  }

  /**
   * 构建故障排除部分
   */
  buildTroubleshooting() {
    return `## 🔧 常见问题

### 构建错误

**问题**: \`Module not found: Error: Can't resolve 'vue'\`
**解决**: 确保安装了正确版本的 Vue 3

**问题**: \`TypeError: Cannot read property '$on' of undefined\`
**解决**: Vue 3 移除了 $on/$off，使用事件总线库如 mitt

### 组件问题

**问题**: Element UI 组件不工作
**解决**: 迁移到 Element Plus 并更新组件名称

**问题**: 自定义组件报错
**解决**: 检查生命周期钩子名称变更

### 性能问题

**问题**: 页面渲染变慢
**解决**: 检查是否正确使用了 Vue 3 的响应式 API`;
  }

  /**
   * 构建参考资源部分
   */
  buildResources() {
    return `## 📚 参考资源

### 官方文档

- [Vue 3 官方文档](https://v3.vuejs.org/)
- [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
- [Vue Router 4](https://next.router.vuejs.org/)
- [Vuex 4](https://next.vuex.vuejs.org/) / [Pinia](https://pinia.vuejs.org/)

### 组件库

- [Element Plus](https://element-plus.org/)
- [Ant Design Vue 3](https://antdv.com/)
- [Vuetify 3](https://vuetifyjs.com/)

### 工具和插件

- [Vue DevTools](https://devtools.vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Vue CLI](https://cli.vuejs.org/)

### 社区资源

- [Vue 3 生态系统](https://github.com/vuejs/awesome-vue)
- [Vue 3 迁移案例](https://github.com/topics/vue3-migration)`;
  }

  /**
   * 构建文档尾部
   */
  buildFooter() {
    return `---

*本文档由 vue-migrator 工具自动生成。如有问题，请参考官方文档或寻求社区帮助。*

**祝您迁移顺利！** 🎉`;
  }
}

module.exports = MigrationDocGenerator;
