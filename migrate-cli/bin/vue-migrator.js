#!/usr/bin/env node

const { Command } = require('commander');
const chalk = require('chalk');
const path = require('path');
const fs = require('fs-extra');
const ora = require('ora');

// 导入所有模块
const PackageUpgrader = require('../src/dependency/packageUpgrader');
const PackageComparator = require('../src/dependency/packageComparator');
const DependencyChecker = require('../src/dependency/dependencyChecker');
const CodeMigrator = require('../src/codeMigrator');
const ComponentMigrator = require('../src/componentMigrator');
const ViewMigrator = require('../src/viewMigrator');
const FailureLogger = require('../src/failureLogger');
const AIRepairer = require('../src/aiRepairer');
const ESLintFixer = require('../src/eslintFixer');
const BuildFixer = require('../src/buildFixer');
const AutoMigrator = require('../src/core/autoMigrator');

/**
 * 统一的 Vue 2 到 Vue 3 迁移工具
 * 整合了完整迁移和分步执行功能
 */
class UnifiedVueMigrator {
  constructor(projectPath, options = {}) {
    this.projectPath = path.resolve(projectPath);
    this.options = {
      skipDependencyCheck: options.skipDependencyCheck || false,
      skipAIRepair: options.skipAIRepair || false,
      skipESLint: options.skipESLint !== undefined ? options.skipESLint : true,
      skipBuild: options.skipBuild || false,
      aiApiKey: options.aiApiKey || process.env.OPENAI_API_KEY,
      buildCommand: options.buildCommand || 'npm run build',
      dryRun: options.dryRun || false,
      verbose: options.verbose || false,
      ...options
    };

    this.stats = {
      startTime: Date.now(),
      endTime: null,
      totalSteps: 7,
      completedSteps: 0,
      success: false,
      errors: [],
      stepResults: {}
    };

    this.failedFiles = [];
    this.spinner = null;
  }

  /**
   * 执行完整的 7 步迁移流程
   */
  async migrate() {
    try {
      this.printHeader();

      // 验证项目
      await this.validateProject();

      // 执行 7 个步骤
      await this.step1_upgradePackageJson();
      await this.step2_checkDependencies();
      await this.step3_migrateCode();
      await this.step4_logFailures();
      await this.step5_aiRepair();
      await this.step6_eslintFix();
      await this.step7_buildAndFix();

      // 完成迁移
      await this.completeMigration();
    } catch (error) {
      await this.handleMigrationError(error);
      throw error;
    }
  }

  /**
   * 执行单个步骤
   */
  async executeStep(stepNumber) {
    try {
      this.printHeader();
      await this.validateProject();

      switch (stepNumber) {
        case 1:
          await this.step1_upgradePackageJson();
          break;
        case 2:
          await this.step2_checkDependencies();
          break;
        case 3:
          await this.step3_migrateCode();
          break;
        case 4:
          await this.step4_logFailures();
          break;
        case 5:
          await this.step5_aiRepair();
          break;
        case 6:
          await this.step6_eslintFix();
          break;
        case 7:
          await this.step7_buildAndFix();
          break;
        default:
          throw new Error(`无效的步骤号: ${stepNumber}`);
      }

      console.log(chalk.green(`\n✅ 步骤 ${stepNumber} 执行完成！`));

    } catch (error) {
      console.error(chalk.red(`\n❌ 步骤 ${stepNumber} 执行失败:`), error.message);
      throw error;
    }
  }

  printHeader() {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 统一迁移工具\n'));
    console.log(chalk.gray(`项目路径: ${this.projectPath}`));
    console.log(chalk.gray(`开始时间: ${new Date().toLocaleString()}`));
    console.log(chalk.gray(`模式: ${this.options.dryRun ? '预览模式' : '实际执行'}\n`));
  }

  async validateProject() {
    this.spinner = ora('验证项目结构...').start();

    try {
      // 检查项目目录是否存在
      if (!await fs.pathExists(this.projectPath)) {
        throw new Error(`项目目录不存在: ${this.projectPath}`);
      }

      // 检查 package.json
      const packageJsonPath = path.join(this.projectPath, 'package.json');
      if (!await fs.pathExists(packageJsonPath)) {
        throw new Error('未找到 package.json 文件');
      }

      // 检查是否为 Vue 项目
      const packageJson = await fs.readJson(packageJsonPath);
      if (!packageJson.dependencies?.vue && !packageJson.devDependencies?.vue) {
        throw new Error('这不是一个 Vue 项目');
      }

      // 检查 Vue 版本
      const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
      if (vueVersion && vueVersion.startsWith('3.')) {
        console.log(chalk.yellow('⚠️  检测到 Vue 3 项目，可能不需要迁移'));
      }

      this.spinner.succeed('项目验证通过');
    } catch (error) {
      this.spinner.fail('项目验证失败');
      throw error;
    }
  }

  /**
   * 步骤 1: 升级 package.json 依赖
   */
  async step1_upgradePackageJson() {
    if (this.options.skipDependencyCheck && this.stats.completedSteps >= 1) {
      this.skipStep('package.json 依赖升级');
      return;
    }

    this.spinner = ora('步骤 1/7: 升级 package.json 依赖...').start();

    try {
      const upgrader = new PackageUpgrader(this.projectPath);
      const result = await upgrader.upgrade();

      this.stats.stepResults.packageUpgrade = result;
      this.completeStep();

      this.spinner.succeed('步骤 1/7: package.json 依赖升级完成');

      if (this.options.verbose && result.changes?.length > 0) {
        console.log(chalk.gray('  变更详情:'));
        result.changes.forEach(change => console.log(chalk.gray(`    ${change}`)));
      }
    } catch (error) {
      this.spinner.fail('步骤 1/7: package.json 依赖升级失败');
      this.stats.errors.push({ step: 1, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 2: 检查依赖兼容性
   */
  async step2_checkDependencies() {
    if (this.options.skipDependencyCheck) {
      this.skipStep('依赖兼容性检查');
      return;
    }

    this.spinner = ora('步骤 2/7: 检查依赖兼容性...').start();

    try {
      const checker = new DependencyChecker(this.projectPath);
      const result = await checker.checkCompatibility();

      this.stats.stepResults.dependencyCheck = result;
      this.completeStep();

      this.spinner.succeed('步骤 2/7: 依赖兼容性检查完成');

      // 如果有不兼容的依赖，给出警告
      if (result.incompatible?.length > 0) {
        console.log(chalk.yellow(`⚠️  发现 ${result.incompatible.length} 个不兼容的依赖`));
        if (this.options.verbose) {
          result.incompatible.forEach(dep => console.log(chalk.gray(`    ${dep}`)));
        }
      }
    } catch (error) {
      this.spinner.fail('步骤 2/7: 依赖兼容性检查失败');
      this.stats.errors.push({ step: 2, error: error.message });
      // 这个步骤失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  依赖检查失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 3: 批量迁移代码文件
   */
  async step3_migrateCode() {
    this.spinner = ora('步骤 3/7: 批量迁移代码文件...').start();

    try {
      const migrator = new CodeMigrator(this.projectPath);
      const result = await migrator.migrate();

      this.stats.stepResults.codeMigration = result;
      this.failedFiles = migrator.getFailedFiles();

      // 如果有失败文件，显示详细信息
      if (this.failedFiles.length > 0 && this.options.verbose) {
        console.log(chalk.yellow(`\n⚠️  发现 ${this.failedFiles.length} 个转换失败的文件:`));
        this.failedFiles.forEach(file => {
          console.log(chalk.gray(`  ${file.file} - ${file.errorType}: ${file.error.substring(0, 100)}...`));
        });
      }

      this.completeStep();

      this.spinner.succeed('步骤 3/7: 代码文件迁移完成');

      if (this.options.verbose) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 3/7: 代码文件迁移失败');
      this.stats.errors.push({ step: 3, error: error.message });
      throw error;
    }
  }

  /**
   * 步骤 4: 记录失败文件
   */
  async step4_logFailures() {
    this.spinner = ora('步骤 4/7: 记录失败文件...').start();

    try {
      const failureLogger = new FailureLogger(this.projectPath);
      await failureLogger.initialize();

      // 记录所有失败的文件
      if (this.failedFiles && this.failedFiles.length > 0) {
        for (const failedFile of this.failedFiles) {
          await failureLogger.logFailure(
            failedFile.file,
            new Error(failedFile.error),
            { step: 'code-migration' }
          );
        }
      }

      await failureLogger.saveFailures();
      this.stats.stepResults.failureLogging = {
        failedCount: this.failedFiles.length
      };
      this.completeStep();

      if (this.failedFiles.length > 0) {
        this.spinner.warn(`步骤 4/7: 记录了 ${this.failedFiles.length} 个失败文件`);
      } else {
        this.spinner.succeed('步骤 4/7: 没有失败文件需要记录');
      }
    } catch (error) {
      this.spinner.fail('步骤 4/7: 失败文件记录失败');
      this.stats.errors.push({ step: 4, error: error.message });
      // 这个步骤失败不应该中断整个流程
      this.completeStep();
    }
  }

  /**
   * 步骤 5: AI 修复失败文件
   */
  async step5_aiRepair() {
    if (this.options.skipAIRepair) {
      this.skipStep('AI 修复');
      return;
    }

    this.spinner = ora('步骤 5/7: AI 修复失败文件...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });

      if (!aiRepairer.isEnabled()) {
        this.spinner.warn('步骤 5/7: AI 修复不可用（缺少 API Key）');
        this.completeStep();
        return;
      }

      if (this.failedFiles.length === 0) {
        this.spinner.succeed('步骤 5/7: 没有需要 AI 修复的文件');
        this.completeStep();
        return;
      }

      console.log(chalk.blue(`\n🤖 准备使用 AI 修复 ${this.failedFiles.length} 个失败文件...`));

      // 按错误类型分组显示
      const errorGroups = {};
      this.failedFiles.forEach(file => {
        if (!errorGroups[file.errorType]) {
          errorGroups[file.errorType] = [];
        }
        errorGroups[file.errorType].push(file);
      });

      if (this.options.verbose) {
        Object.entries(errorGroups).forEach(([errorType, files]) => {
          console.log(chalk.gray(`  ${errorType}: ${files.length} 个文件`));
        });
      }

      const result = await aiRepairer.repairFailedFiles(this.failedFiles, this.projectPath);
      this.stats.stepResults.aiRepair = result;
      this.completeStep();

      this.spinner.succeed('步骤 5/7: AI 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  成功: ${result.success || 0}, 失败: ${result.failed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 5/7: AI 修复失败');
      this.stats.errors.push({ step: 5, error: error.message });
      // AI 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  AI 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 6: ESLint 自动修复
   */
  async step6_eslintFix() {
    if (this.options.skipESLint) {
      this.skipStep('ESLint 修复');
      return;
    }

    this.spinner = ora('步骤 6/7: ESLint 自动修复...').start();

    try {
      const eslintFixer = new ESLintFixer(this.projectPath);

      // 检查 ESLint 是否可用
      if (!await eslintFixer.isESLintAvailable()) {
        this.spinner.warn('步骤 6/7: ESLint 不可用，跳过此步骤');
        this.completeStep();
        return;
      }

      const result = await eslintFixer.fix();
      this.stats.stepResults.eslintFix = result;
      this.completeStep();

      this.spinner.succeed('步骤 6/7: ESLint 修复完成');

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  修复文件: ${result.filesFixed || 0}, 修复错误: ${result.errorsFixed || 0}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 6/7: ESLint 修复失败');
      this.stats.errors.push({ step: 6, error: error.message });
      // ESLint 修复失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  ESLint 修复失败，继续后续步骤'));
      this.completeStep();
    }
  }

  /**
   * 步骤 7: 构建项目并修复错误
   */
  async step7_buildAndFix() {
    if (this.options.skipBuild) {
      this.skipStep('构建修复');
      return;
    }

    this.spinner = ora('步骤 7/7: 构建项目并修复错误...').start();

    try {
      const aiRepairer = new AIRepairer({ apiKey: this.options.aiApiKey });
      const buildFixer = new BuildFixer(this.projectPath, {
        buildCommand: this.options.buildCommand,
        aiRepairer: aiRepairer.isEnabled() ? aiRepairer : null
      });

      const result = await buildFixer.buildAndFix();
      this.stats.stepResults.buildFix = result;
      this.completeStep();

      if (result.success) {
        this.spinner.succeed('步骤 7/7: 项目构建成功');
      } else {
        this.spinner.warn('步骤 7/7: 项目构建仍有问题，需要手动修复');
      }

      if (this.options.verbose && result) {
        console.log(chalk.gray(`  构建尝试: ${result.buildAttempts || 1}, 最终成功: ${result.success ? '是' : '否'}`));
      }
    } catch (error) {
      this.spinner.fail('步骤 7/7: 构建修复失败');
      this.stats.errors.push({ step: 7, error: error.message });
      // 构建失败不应该中断整个流程
      console.log(chalk.yellow('⚠️  构建修复失败，可能需要手动处理'));
      this.completeStep();
    }
  }

  /**
   * 完成迁移
   */
  async completeMigration() {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = this.stats.errors.length === 0;

    console.log('\n' + chalk.bold.green('🎉 Vue 2 到 Vue 3 迁移完成!'));
    this.printFinalStats();

    // 生成迁移报告
    await this.generateMigrationReport();

    // 显示后续建议
    this.printRecommendations();
  }

  /**
   * 处理迁移错误
   */
  async handleMigrationError(error) {
    this.stats.endTime = Date.now();
    this.stats.duration = this.stats.endTime - this.stats.startTime;
    this.stats.success = false;

    console.log('\n' + chalk.bold.red('❌ 迁移过程中发生错误'));
    console.log(chalk.red(error.message));

    if (this.options.verbose) {
      console.log(chalk.gray(error.stack));
    }

    await this.generateMigrationReport();
  }

  /**
   * 跳过步骤
   */
  skipStep(stepName) {
    console.log(chalk.gray(`⏭️  跳过: ${stepName}`));
    this.completeStep();
  }

  /**
   * 完成步骤
   */
  completeStep() {
    this.stats.completedSteps++;
  }

  /**
   * 打印最终统计
   */
  printFinalStats() {
    const duration = Math.round(this.stats.duration / 1000);

    console.log('\n' + chalk.bold('📊 迁移统计:'));
    console.log(`耗时: ${duration} 秒`);
    console.log(`完成步骤: ${this.stats.completedSteps}/${this.stats.totalSteps}`);
    console.log(`错误数量: ${this.stats.errors.length}`);

    if (this.stats.stepResults.codeMigration) {
      const cm = this.stats.stepResults.codeMigration;
      console.log(`代码文件: ${cm.success || 0} 成功, ${cm.failed || 0} 失败`);
    }

    if (this.stats.stepResults.buildFix?.success) {
      console.log(chalk.green('✅ 项目可以成功构建'));
    } else if (this.stats.stepResults.buildFix) {
      console.log(chalk.yellow('⚠️  项目构建可能仍有问题'));
    }
  }

  /**
   * 生成迁移报告
   */
  async generateMigrationReport() {
    const reportPath = path.join(this.projectPath, 'migration-report.json');

    const report = {
      timestamp: new Date().toISOString(),
      projectPath: this.projectPath,
      options: this.options,
      stats: this.stats,
      success: this.stats.success,
      duration: this.stats.duration,
      recommendations: this.generateRecommendations()
    };

    if (!this.options.dryRun) {
      await fs.writeJson(reportPath, report, { spaces: 2 });
    }

    console.log(chalk.blue(`📄 迁移报告已生成: ${reportPath}`));
  }

  /**
   * 生成建议
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.stats.errors.length > 0) {
      recommendations.push('检查错误日志并手动修复剩余问题');
    }

    if (this.stats.stepResults.codeMigration?.failed > 0) {
      recommendations.push('手动检查迁移失败的文件');
    }

    if (!this.stats.stepResults.buildFix?.success) {
      recommendations.push('运行构建命令检查剩余的构建错误');
    }

    recommendations.push('运行 npm install 安装新依赖');
    recommendations.push('运行测试确保功能正常');
    recommendations.push('检查 UI 组件是否正确迁移到 Element Plus');
    recommendations.push('更新文档和部署配置');

    return recommendations;
  }

  /**
   * 打印建议
   */
  printRecommendations() {
    const recommendations = this.generateRecommendations();

    if (recommendations.length > 0) {
      console.log('\n' + chalk.bold('💡 后续建议:'));
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
  }

}

// CLI 接口
const program = new Command();

program
  .name('vue-migrator')
  .description('Vue 2 到 Vue 3 统一迁移工具')
  .version('1.0.0');

// 自动迁移命令（推荐）
program
  .command('auto [project-path] [destination-path]')
  .description('🚀 自动检测项目类型并执行最佳迁移策略（推荐）')
  .option('--force', '强制迁移（即使检测到 Vue 3 项目）')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--skip-backup', '跳过项目备份')
  .option('--ai-key <key>', 'AI API Key (支持 OpenAI/DeepSeek/GLM)')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, destinationPath, options) => {
    try {
      const sourcePath = path.resolve(projectPath || process.cwd());
      
      // 如果提供了目标路径，则为新目录迁移模式
      if (destinationPath) {
        const destPath = path.resolve(destinationPath);
        
        console.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 新目录模式\n'));
        console.log(chalk.gray(`源项目: ${sourcePath}`));
        console.log(chalk.gray(`目标项目: ${destPath}`));
        
        // 验证源项目存在
        if (!await fs.pathExists(sourcePath)) {
          throw new Error(`源项目路径不存在: ${sourcePath}`);
        }
        
        // 检查目标路径是否已存在
        if (await fs.pathExists(destPath)) {
          if (!options.force) {
            throw new Error(`目标路径已存在: ${destPath}。使用 --force 强制覆盖`);
          }
          console.log(chalk.yellow(`⚠️  目标路径已存在，将被覆盖: ${destPath}`));
        }
        
        // 使用增强的 AutoMigrator 进行新目录迁移
        const autoMigrator = new AutoMigrator(sourcePath, {
          ...options,
          destinationPath: destPath,
          newDirectoryMode: true
        });
        
        await autoMigrator.migrate();
        
      } else {
        // 原地迁移模式（保持原有行为）
        console.log(chalk.bold.blue('\n🚀 Vue 自动化迁移工具 - 原地迁移模式\n'));
        console.log(chalk.gray(`项目路径: ${sourcePath}`));
        
        const autoMigrator = new AutoMigrator(sourcePath, options);
        await autoMigrator.migrate();
      }

    } catch (error) {
      console.error(chalk.red('\n❌ 自动迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 完整迁移命令
program
  .command('migrate')
  .description('执行完整的 Vue 2 到 Vue 3 迁移（7个步骤）')
  .argument('[project-path]', '项目路径', process.cwd())
  .option('--skip-dependency-check', '跳过依赖兼容性检查')
  .option('--skip-ai', '跳过 AI 修复步骤')
  .option('--eslint', '启用 ESLint 自动修复（默认禁用）')
  .option('--skip-build', '跳过构建和构建错误修复')
  .option('--skip-strategy', '跳过迁移策略分析')
  .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath, options) => {
    try {
      const migrator = new UnifiedVueMigrator(projectPath, {
        skipDependencyCheck: options.skipDependencyCheck,
        skipAIRepair: options.skipAi,
        skipESLint: !options.eslint,
        skipBuild: options.skipBuild,
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await migrator.migrate();

    } catch (error) {
      console.error(chalk.red('\n❌ 迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 单步执行命令
program
  .command('step <number> [project-path]')
  .description('执行指定的迁移步骤 (1-7)')
  .option('--ai-key <key>', 'OpenAI API Key')
  .option('--build-command <cmd>', '构建命令', 'npm run build')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (stepNumber, projectPath = process.cwd(), options) => {
    try {
      const step = parseInt(stepNumber);
      if (step < 1 || step > 7) {
        console.error(chalk.red('❌ 步骤号必须在 1-7 之间'));
        process.exit(1);
      }

      const migrator = new UnifiedVueMigrator(projectPath, {
        aiApiKey: options.aiKey,
        buildCommand: options.buildCommand,
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await migrator.executeStep(step);

    } catch (error) {
      console.error(chalk.red('\n❌ 步骤执行失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 新旧工程迁移命令
program
  .command('migrate-to <old-project> <new-project>')
  .description('从旧 Vue 2 工程迁移到新 Vue 3 工程')
  .option('--compare-only', '仅对比 package.json，不执行迁移')
  .option('--components-only', '仅迁移组件')
  .option('--views-only', '仅迁移视图')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      console.log(chalk.bold.blue('\n🔄 Vue 2 到 Vue 3 工程迁移工具\n'));
      console.log(chalk.gray(`旧工程: ${oldProjectPath}`));
      console.log(chalk.gray(`新工程: ${newProjectPath}`));

      // 验证项目路径
      if (!await fs.pathExists(oldProjectPath)) {
        throw new Error(`旧项目路径不存在: ${oldProjectPath}`);
      }
      if (!await fs.pathExists(newProjectPath)) {
        throw new Error(`新项目路径不存在: ${newProjectPath}`);
      }

      // 1. 对比 package.json
      console.log(chalk.blue('\n📦 步骤 1: 对比 package.json'));
      const comparator = new PackageComparator(oldProjectPath, newProjectPath);
      const comparison = await comparator.compare();

      if (options.compareOnly) {
        console.log(chalk.green('\n✅ package.json 对比完成！'));
        return;
      }

      // 2. 迁移组件
      if (!options.viewsOnly) {
        console.log(chalk.blue('\n🧩 步骤 2: 迁移组件'));
        const componentMigrator = new ComponentMigrator(oldProjectPath, newProjectPath, {
          dryRun: options.dryRun,
          verbose: options.verbose
        });
        await componentMigrator.migrateComponents();
      }

      // 3. 迁移视图
      if (!options.componentsOnly) {
        console.log(chalk.blue('\n📄 步骤 3: 迁移视图'));
        const viewMigrator = new ViewMigrator(oldProjectPath, newProjectPath, {
          dryRun: options.dryRun,
          verbose: options.verbose
        });
        await viewMigrator.migrateViews();
      }

      // 4. 生成迁移报告
      console.log(chalk.blue('\n📄 步骤 4: 生成迁移报告'));
      await generateMigrationReport(oldProjectPath, newProjectPath);

      console.log(chalk.bold.green('\n🎉 工程迁移完成！'));
      console.log(chalk.yellow('\n💡 后续建议:'));
      console.log('1. 检查迁移后的代码是否正常工作');
      console.log('2. 运行测试确保功能正常');
      console.log('3. 更新路由和状态管理配置');
      console.log('4. 检查样式是否需要调整');
      console.log('5. 查看生成的 migration-report.md 了解详细迁移信息');

    } catch (error) {
      console.error(chalk.red('\n❌ 工程迁移失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 生成迁移报告的辅助函数
async function generateMigrationReport(oldProjectPath, newProjectPath) {
  try {
    const MigrationDocGenerator = require('../src/migrator/migrationDocGenerator');

    console.log(chalk.gray('正在生成迁移报告...'));

    // 使用旧项目的 package.json 生成分析结果
    const docGenerator = new MigrationDocGenerator(oldProjectPath, null, {
      outputPath: path.join(newProjectPath, 'migration-report.md'),
      includeUsageDetails: false,
      language: 'zh-CN'
    });

    const result = await docGenerator.generateMigrationGuide();

    if (result.success) {
      console.log(chalk.green(`✅ 迁移报告已生成: ${result.outputPath}`));
    } else {
      console.log(chalk.yellow('⚠️  迁移报告生成失败'));
    }
  } catch (error) {
    console.log(chalk.yellow('⚠️  迁移报告生成失败:'), error.message);
  }
}

// 对比 package.json 命令
program
  .command('compare <old-project> <new-project>')
  .description('对比新旧工程的 package.json')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const comparator = new PackageComparator(oldProjectPath, newProjectPath);
      const comparison = await comparator.compare();

      if (options.verbose) {
        console.log('\n详细对比结果:', JSON.stringify(comparison, null, 2));
      }

    } catch (error) {
      console.error(chalk.red('\n❌ 对比失败:'), error.message);
      process.exit(1);
    }
  });

// 迁移组件命令
program
  .command('migrate-components <old-project> <new-project>')
  .description('迁移组件从旧工程到新工程')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const componentMigrator = new ComponentMigrator(oldProjectPath, newProjectPath, {
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await componentMigrator.migrateComponents();

    } catch (error) {
      console.error(chalk.red('\n❌ 组件迁移失败:'), error.message);
      process.exit(1);
    }
  });

// 迁移视图命令
program
  .command('migrate-views <old-project> <new-project>')
  .description('迁移视图从旧工程到新工程')
  .option('--dry-run', '预览模式，不实际修改文件')
  .option('--verbose', '显示详细信息')
  .action(async (oldProject, newProject, options) => {
    try {
      const oldProjectPath = path.resolve(oldProject);
      const newProjectPath = path.resolve(newProject);

      const viewMigrator = new ViewMigrator(oldProjectPath, newProjectPath, {
        dryRun: options.dryRun,
        verbose: options.verbose
      });

      await viewMigrator.migrateViews();

    } catch (error) {
      console.error(chalk.red('\n❌ 视图迁移失败:'), error.message);
      process.exit(1);
    }
  });

// 迁移策略分析命令
program
  .command('analyze [project-path]')
  .description('分析项目并生成迁移策略和文档')
  .option('--ai-key <key>', 'AI API Key (支持 DeepSeek/GLM/OpenAI)')
  .option('--output <path>', '输出文档路径', 'migration-guide.md')
  .option('--verbose', '显示详细信息')
  .action(async (projectPath = process.cwd(), options) => {
    try {
      const MigrationStrategySelector = require('../src/migrator/migrationStrategySelector');
      const MigrationDocGenerator = require('../src/migrator/migrationDocGenerator');

      console.log(chalk.bold.blue('\n🎯 Vue 2 到 Vue 3 迁移策略分析\n'));
      console.log(chalk.gray(`项目路径: ${path.resolve(projectPath)}`));

      // 执行策略分析
      const strategySelector = new MigrationStrategySelector(projectPath, {
        aiApiKey: options.aiKey,
        verbose: options.verbose
      });

      const strategyResult = await strategySelector.selectStrategy();

      console.log('\n' + chalk.bold('📊 分析结果:'));
      console.log(`选择策略: ${strategyResult.strategy === 'ai-assisted' ? '🤖 AI 辅助迁移' : '📖 文档指导迁移'}`);
      console.log(`AI 服务: ${strategyResult.aiAvailable ? '✅ 可用' : '❌ 不可用'}`);

      // 生成迁移文档
      const docGenerator = new MigrationDocGenerator(projectPath, strategyResult.analysisResult, {
        outputPath: path.resolve(projectPath, options.output)
      });

      await docGenerator.generateMigrationGuide();

      // 根据策略执行相应操作
      if (strategyResult.strategy === 'ai-assisted') {
        console.log(chalk.green('\n🤖 AI 辅助策略建议:'));
        const migrationPlan = await strategySelector.executeAIAssistedStrategy();
        console.log('- 使用 AI 进行智能代码转换');
        console.log('- 自动处理复杂的迁移场景');
        console.log('- 提供精确的代码修改建议');
      } else {
        console.log(chalk.yellow('\n📖 文档指导策略建议:'));
        const migrationGuide = await strategySelector.executeDocumentationGuidedStrategy();
        console.log('- 参考生成的迁移指导文档');
        console.log('- 按步骤手动执行迁移');
        console.log('- 查阅相关组件的迁移文档');
      }

      console.log(chalk.bold.green('\n✅ 策略分析完成！'));
      console.log(chalk.blue(`📄 迁移指导文档: ${options.output}`));

    } catch (error) {
      console.error(chalk.red('\n❌ 策略分析失败:'), error.message);
      if (process.env.DEBUG) {
        console.error(error.stack);
      }
      process.exit(1);
    }
  });

// 显示步骤说明
program
  .command('steps')
  .description('显示迁移步骤说明')
  .action(() => {
    console.log(chalk.bold.blue('\n🚀 Vue 2 到 Vue 3 迁移步骤说明\n'));

    console.log(chalk.bold.green('🎯 自动迁移模式（强烈推荐）:'));
    console.log(chalk.green('vue-migrator auto [project-path]'));
    console.log(chalk.gray('- 🔍 自动检测项目类型（Vue Element Admin、Vue 2、Vue 3）'));
    console.log(chalk.gray('- ⚙️  自动应用最佳配置（无需手动输入参数）'));
    console.log(chalk.gray('- 🤖 智能选择 AI 提供商（DeepSeek > GLM > OpenAI）'));
    console.log(chalk.gray('- 📦 自动备份项目文件'));
    console.log(chalk.gray('- 🚀 一键完成完整迁移流程\n'));

    console.log(chalk.bold('传统迁移模式（原地修改）:'));
    const steps = [
      '1. 升级 package.json 依赖 - 将 Vue 相关依赖升级到 Vue 3 版本',
      '2. 检查依赖兼容性 - 检查第三方依赖是否支持 Vue 3',
      '3. 批量迁移代码文件 - 使用 Gogocode 转换 .vue 和 .js 文件',
      '4. 记录失败文件 - 记录转换失败的文件供后续处理',
      '5. AI 修复失败文件 - 使用 AI 自动修复转换失败的文件',
      '6. ESLint 自动修复 - 运行 ESLint 修复格式和语法问题',
      '7. 构建项目并修复错误 - 尝试构建项目并使用 AI 修复构建错误'
    ];

    steps.forEach((step, index) => {
      console.log(chalk.green(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.bold('新工程迁移模式:'));
    const newSteps = [
      '1. 对比 package.json - 分析新旧工程的依赖差异',
      '2. 迁移组件 - 将 components 目录转换并复制到新工程',
      '3. 迁移视图 - 将 views 目录转换并复制到新工程'
    ];

    newSteps.forEach((step, index) => {
      console.log(chalk.blue(`${index + 1}. ${step.split(' - ')[0]}`));
      console.log(chalk.gray(`   ${step.split(' - ')[1]}\n`));
    });

    console.log(chalk.yellow('💡 使用建议:'));
    console.log(chalk.bold.green('🎯 自动模式（推荐）:'));
    console.log('- vue-migrator auto                    # 自动迁移当前目录项目');
    console.log('- vue-migrator auto /path/to/project   # 自动迁移指定项目');
    console.log('- vue-migrator auto --dry-run          # 预览模式，不实际修改');
    console.log('- vue-migrator auto --ai-key <key>     # 指定 AI API Key');
    console.log('\n📖 策略分析:');
    console.log('- vue-migrator analyze                 # 分析项目并生成迁移策略');
    console.log('\n⚙️  传统模式:');
    console.log('- vue-migrator migrate                 # 执行完整迁移');
    console.log('- vue-migrator step <number>           # 执行单个步骤');
    console.log('\n🔄 新工程模式:');
    console.log('- vue-migrator migrate-to <old> <new> # 从旧工程迁移到新工程');
    console.log('- vue-migrator compare <old> <new>     # 仅对比 package.json');
  });

// 全局错误处理
process.on('uncaughtException', (error) => {
  console.error(chalk.red('❌ 未捕获的异常:'), error.message);
  if (process.env.DEBUG) {
    console.error(error.stack);
  }
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('❌ 未处理的 Promise 拒绝:'), reason);
  if (process.env.DEBUG) {
    console.error(promise);
  }
  process.exit(1);
});

// 解析命令行参数
program.parse();

// 如果没有提供命令，显示帮助
if (!process.argv.slice(2).length) {
  program.outputHelp();
}

module.exports = UnifiedVueMigrator;
