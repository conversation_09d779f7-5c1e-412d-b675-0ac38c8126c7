# Vue 2 到 Vue 3 迁移指导文档

> 项目: vue-element-admin  
> 生成时间: 2025/6/18 12:28:26  
> 工具版本: vue-migrator v1.0.0

本文档基于项目分析结果自动生成，提供详细的 Vue 2 到 Vue 3 迁移指导。

## 📋 目录

- [迁移概览](#迁移概览)
- [依赖迁移指南](#依赖迁移指南)
- [代码迁移指南](#代码迁移指南)
- [分步迁移指南](#分步迁移指南)
- [常见问题](#常见问题)
- [参考资源](#参考资源)

## 📊 迁移概览

### 分析结果

- **总依赖数**: 54
- **有迁移文档**: 4
- **无迁移文档**: 50
- **扫描文件数**: 0
- **发现使用**: 0 处

### 迁移复杂度评估

**复杂度**: 复杂

存在较多没有迁移文档的依赖，需要手动研究迁移方案

### 预估时间

**预估时间**: 129 小时 (约 17 个工作日)

*注意: 实际时间可能因项目复杂度和团队经验而有所不同*

## 📚 可用迁移文档

本工具包含以下组件的详细迁移指导文档：

- **riophae-vue-treeselect**: riophae-vue-treeselect
- **tinymce-tinymce-vue**: 从 @tinymce/tinymce-vue (Vue 2) 迁移到 @tinymce/tinymce-vue (Vue 3)
- **v-charts**: v-charts
- **vue-calendar-component**: vue-calendar-component
- **vue-count-to**: vue-count-to
- **vue-json-pretty**: 迁移 vue-json-pretty
---

# 迁移指南: `vue-json-pretty`

本指南详细介绍了将 `vue-json-pretty` 从 Vue 2 项目迁移到 Vue 3 项目的过程。该库为 Vue 的每个版本提供了不同的主版本，因此迁移主要涉及更新包版本和适应一些 API 的更改。

## 主要变化

1.  **NPM 包版本**: Vue 2 项目使用 `v1.x` 版本的 `vue-json-pretty`，而 Vue 3 项目应使用最新的 `v2.x` 或更高版本。
2.  **事件名称**: 事件名称已从 kebab-case (`node-click`) 更新为 camelCase (`nodeClick`)，以更好地与现代 JavaScript 约定保持一致。
3.  **`.sync` 修饰符**: 已弃用的 `.sync` 修饰符（用于像 `selectedValue` 这样的 props）已被 `v-model` 替换。
4.  **CSS 导入**: 手动导入样式表的需求保持不变。

---

## 分步迁移指南

### 1. 更新 `package.json`

第一步是更新 `package.json` 文件中 `vue-json-pretty` 的版本，并安装新版本。

**对于 Vue 2，你的 `package.json` 会是这样:**
```json
- **vue-scrollbars**: 从 vue-scrollbars 迁移到 vue3-perfect-scrollbar
- **vue-splitpane**: vue-splitpane
- **vue-template-compiler**: 迁移 vue-template-compiler
---

# 迁移指南: `vue-template-compiler` 到 `@vue/compiler-sfc`

本指南解释了从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的迁移。与其他关注 UI 组件的指南不同，本指南处理的是 Vue 构建过程的核心部分。对于许多使用标准工具（如 Vue CLI 或 Vite）的开发者来说，此更改将在项目升级过程中自动处理。

然而，如果你有一个自定义的构建设置（例如，一个自定义的 Webpack 配置），理解这个变化是至关重要的。

## 编译器的角色

-   在 **Vue 2** 中，`vue-template-compiler` 是一个用于将 `.vue` 文件中的 `<template>` 块编译成 JavaScript `render` 函数的包。它的版本必须与核心 `vue` 包保持同步。

-   在 **Vue 3** 中，这个职责被分开了。新的 `@vue/compiler-sfc` 包负责解析单文件组件（SFCs），而实际的模板编译则由 `@vue/compiler-dom` 处理，它现在是 `vue` 包本身的核心依赖项。

从本质上讲，`@vue/compiler-sfc` 是构建工具用来理解和处理 `.vue` 文件的工具。

---

## 分步迁移指南

### 1. 依赖更新

第一步是调整你项目的 `devDependencies`。

1.  **移除 `vue-template-compiler`**: 在 Vue 3 项目中不再需要这个包。

    ```bash
    npm uninstall vue-template-compiler
    ```

2.  **检查 `@vue/compiler-sfc`**: 这个包是现代 Vue 构建插件（Webpack 的 `vue-loader`，Vite 的 `@vitejs/plugin-vue`）的对等依赖（peer dependency）。在大多数情况下，你 **不** 需要手动安装它。它会随着构建插件一起被安装。如果由于特定原因需要添加它，它应该是一个 `devDependency`。

    ```bash
    # 仅在你的特定设置需要时
    npm install -D @vue/compiler-sfc
    ```

### 2. 构建配置更新

这是最可能发生重大手动更改的地方，具体取决于你的构建工具。

#### 对于 Vue CLI 用户

当你使用官方迁移命令（`vue upgrade`）将项目升级到 Vue 3 时，Vue CLI 会自动更新你的 Webpack 配置、`vue-loader` 以及所有相关依赖。这个变更会自动为你处理。

#### 对于 Vite 用户

如果你正在使用 Vite 创建一个新的 Vue 3 项目或迁移现有项目，`@vitejs/plugin-vue` 插件会处理 SFC 编译。它在底层使用 `@vue/compiler-sfc`，你无需进行任何手动配置。

#### 对于自定义 Webpack 用户

如果你管理自己的 Webpack 配置，你需要进行以下更改：

1.  **更新 `vue-loader`**: 你必须将 `vue-loader` 更新到版本 16 或更高。版本 16 是为 Vue 3 设计的，并使用 `@vue/compiler-sfc`。

    ```bash
    npm install -D vue-loader@^16.0.0
    ```

2.  **更新 Webpack 配置**: `vue-loader` 插件的导入方式已更改。

    **Vue 2 `webpack.config.js`:**
    ```javascript
- **vue-text-format**: 迁移 vue-text-format
---

# 迁移指南: `vue-text-format`

本指南将引导你将 `vue-text-format` 从 Vue 2 项目迁移到 Vue 3 项目。

`vue-text-format` 是一个提供强大文本格式化功能的库，类似于 Excel 等电子表格应用程序中的功能。幸运的是，该库与 Vue 2 和 Vue 3 都兼容，使得迁移过程相对简单。主要的变化在于更新应用程序入口文件中插件的注册方式。

## 主要变化

1.  **NPM 包**: npm 包名保持不变：`vue-text-format`。你的 `package.json` 中关于包名的部分无需更改，但应确保你使用的是与 Vue 3 兼容的版本。
2.  **插件注册**: 插件的注册方法从 Vue 2 中的 `Vue.use()` 变为 Vue 3 中的 `app.use()`。
3.  **API 用法**: 指令 `v-format` 和全局函数 `$textFormat` 仍然可用。在组合式 API 中访问全局函数的方式略有改变。

---

## 分步迁移指南

### 1. 更新 `main.js` 中的插件注册

主要的变化在你的 `src/main.js` (或 `src/main.ts`) 文件中。你需要从 Vue 2 的插件注册语法切换到 Vue 3 的基于实例的方法。

#### Vue 2 示例 (`main.js`)

在你的 Vue 2 应用中，你会像这样注册插件：

```javascript
- **vue-uuid**: 从 vue-uuid 迁移到 vue3-uuid
- **vue2-tree-org**: 从 vue2-tree-org 迁移到 vue3-tree-org
---

# 迁移指南: `vue2-tree-org` 到 `vue3-tree-org`

本指南旨在帮助你从 `vue2-org-tree` 迁移到其指定的 Vue 3 替代品 `vue3-tree-org`。这两个包都用于渲染组织结构图，但它们是为不同主要版本的 Vue 构建的。

迁移涉及更新包、更改注册方法以及适应新组件的 API。

**重要提示:** `vue3-tree-org` 的文档主要是中文，并托管在一个独立的网站上。本指南基于项目 README 中可用的信息。你可能需要参考[官方 `vue3-tree-org` 文档](https://sangtian152.github.io/vue3-tree-org/)以获取有关 props、事件和插槽的详细 API 细节。

## 主要变化

1.  **NPM 包**: 你将从 `vue2-org-tree` 包切换到 `vue3-tree-org`。
2.  **插件注册**: 初始化从 `Vue.use()` 更改为 `app.use()`。
3.  **CSS 导入**: `vue3-tree-org` 要求你手动导入其样式表。
4.  **组件 API**: 两个版本之间的 props、事件和插槽用法已发生变化。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载 Vue 2 的包并安装 Vue 3 的包。

```bash
npm uninstall vue2-org-tree
npm install vue3-tree-org
```

### 2. 更新插件注册

在你的应用程序入口文件 (`src/main.js` 或 `src/main.ts`) 中，更新插件的注册方式并导入所需的 CSS。

#### Vue 2 示例 (`main.js`)
```javascript
- **vuedraggable**: vuedraggable
- **vuepdf**: 迁移 vuepdf 到 vue3-pdfjs
---

# 迁移指南: `vuepdf` 到 `vue3-pdfjs`

本指南概述了将 `vue-pdf` (一个流行的 Vue 2 PDF 查看器组件) 迁移到 `vue3-pdfjs` 以用于 Vue 3 应用程序的步骤。

**重要提示:** 目标库 `vue3-pdfjs` 已有数年未积极维护。虽然本指南提供了迁移所需的步骤，但你应考虑为 Vue 3 使用更现代且积极维护的替代品，例如 **`@tato30/vue-pdf`** 或 **`pdf-vue3`**。这些库提供更好的支持、更多功能，并且更可能与最新的 Web 标准和 Vue 3 功能兼容。

## 主要差异

- **包名**: 你将从 `vue-pdf` 切换到 `vue3-pdfjs`。
- **插件注册**: `vue3-pdfjs` 需要在你的 Vue 3 应用程序中注册为插件。
- **组件名称**: 组件标签从 `<pdf>` 更改为 `<VuePdf>`。
- **API 和导入**: 两个库都使用 `createLoadingTask` 函数，但导入路径以及你与之交互的方式不同，尤其是在使用组合式 API 时。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载旧包并安装新包。

```bash
npm uninstall vue-pdf
npm install vue3-pdfjs
```

### 2. 注册 Vue 3 插件

在你的应用程序入口文件 (例如, `src/main.js`) 中，你需要注册 `vue3-pdfjs`。

```javascript
// src/main.js
- **wangeditor-editor-for-vue**: 从 @wangeditor/editor-for-vue 迁移到 @wangeditor/editor-for-vue@next

总计 15 个组件的迁移文档可用。

## 📦 依赖迁移指南

### 需要迁移的依赖

以下是检测到的需要迁移的依赖及其处理方案：

#### 📋 其他组件

##### vuedraggable

- **当前版本**: 2.20.0
- **迁移文档**: ✅ 有
- **使用位置**: 0 处

**迁移指导**:

---
source: vuedraggable
target: vue.draggable.next
link: https://github.com/SortableJS/vue.draggable.next
---

# 从 vuedraggable 迁移到 vue.draggable.next

`vue.draggable.next` 是 `vuedraggable` 针对 Vue 3 的官方升级版本，它基于强大的 `Sortable.js`。迁移到新版本需要对代码进行一些重要的修改，主要是关于模板的写法。

## 主要变化

- **安装**: 包名发生了变化。
- **模板语法**: 最大的变化在于如何渲染列表。`vue.draggable.next` 使用了作用域插槽（scoped slot）的 `item` 来代替 `v-for`。
- **`item-key` 属性**: 新增了 `item-key` 属性，用于指定列表中每个元素的唯一标识，这对于 Vue 的渲染性能至关重要。
- **`transition-group` 集成**: 与 `<transition-group>` 的集成方式有所改变。

## 安装

首先，你需要卸载旧版本并安装新版本：

```bash
npm uninstall vuedraggable
npm install vuedraggable@next
```

或者使用 yarn:

```bash
yarn remove vuedraggable
yarn add vuedraggable@next
```

## 使用方法

### 1. 导入组件

在你的组件中导入 `draggable` 组件：

```vue

<script setup>
  import draggable from 'src/migrator/docs/vuedraggable'
</script>
```

### 2. 基本用法迁移

这是迁移过程中最核心的部分。旧的 `vuedraggable` 允许你在组件内部直接使用 `v-for`。而 `vue.draggable.next` 则要求你使用 `#item` 插槽。

**Vue 2 with `vuedraggable`:**
```html
<draggable v-model="myArray" group="people">
  <div v-for="element in myArray" :key="element.id">
    {{ element.name }}
  </div>
</draggable>
```

**Vue 3 with `vue.draggable.next`:**

```vue

<template>
  <draggable
      v-model="myArray"
      group="people"
      @start="drag=true"
      @end="drag=false"
      item-key="id">
    <template #item="{element}">
      <div>{{element.name}}</div>
    </template>
  </draggable>
</template>

<script setup>
  import draggable from 'src/migrator/docs/vuedraggable'
  import { ref } from 'vue'

  const myArray = ref([
    { id: 1, name: 'John' },
    { id: 2, name: 'Joao' },
    { id: 3, name: 'Jean' }
  ])
  const drag = ref(false)
</script>
```

**关键点**:
- `v-for` 被 `<template #item="{element}">` 替代。
- `element` 是插槽暴露出的当前项数据。
- 必须提供 `item-key` 属性，其值对应于数组中对象的唯一键。

### 3. 与 `transition-group` 的集成

如果你使用了 `<transition-group>` 来实现拖动动画，迁移方式如下：

**Vue 2 with `vuedraggable`:**
```html
<draggable v-model="myArray">
  <transition-group name="fade">
    <div v-for="element in myArray" :key="element.id">
      {{ element.name }}
    </div>
  </transition-group>
</draggable>
```

**Vue 3 with `vue.draggable.next`:**
```vue
<template>
  <draggable
    v-model="myArray"
    tag="transition-group"
    :component-data="{ name: 'fade' }"
    item-key="id">
    <template #item="{element}">
      <div>{{element.name}}</div>
    </template>
  </draggable>
</template>

<style>
.fade-move,
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}
.fade-leave-active {
  position: absolute;
}
</style>
```
**关键点**:
- 不再需要嵌套 `<transition-group>` 组件。
- 在 `<draggable>` 上使用 `tag="transition-group"`。
- 使用 `:component-data` 属性来传递给 `<transition-group>` 的 props，例如动画的 `name`。

## 属性和事件

`vue.draggable.next` 继承了 `Sortable.js` 的所有选项作为其属性，例如 `group`, `handle`, `ghost-class`, `sort` 等。事件（如 `start`, `end`, `add`, `remove`, `change`）的用法保持不变。

### Vuex 集成
与 Vuex（或 Pinia）的集成依然简单，通过计算属性的 `get` 和 `set` 即可实现。

```javascript
computed: {
  myList: {
    get() {
      return this.$store.state.myList
    },
    set(value) {
      this.$store.commit('updateList', value)
    }
  }
}
```
然后在模板中：`<draggable v-model="myList" ... >`

## 总结

迁移到 `vue.draggable.next` 需要开发者适应新的基于插槽的 API。虽然这需要修改模板代码，但它带来了更清晰的结构和对 Vue 3 渲染机制更好的兼容性。确保为每个列表项提供 `item-key` 是成功迁移的关键。

更多高级用法和示例，请查阅 [官方文档](https://github.com/SortableJS/vue.draggable.next)。


---

##### 📋 其他 其他组件依赖

以下 39 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **axios** (0.18.1)
- **clipboard** (2.0.4)
- **core-js** (3.6.5)
- **driver.js** (0.9.5)
- **dropzone** (5.5.1)
- **file-saver** (2.0.1)
- **fuse.js** (3.4.4)
- **js-cookie** (2.2.0)
- **jsonlint** (1.6.3)
- **jszip** (3.2.1)
- **normalize.css** (7.0.0)
- **nprogress** (0.2.0)
- **path-to-regexp** (2.4.0)
- **screenfull** (4.2.0)
- **script-loader** (0.7.2)
- **sortablejs** (1.8.4)
- **vue** (2.6.10)
- **xlsx** (0.14.1)
- **autoprefixer** (9.5.1)
- **babel-eslint** (10.1.0)
- **babel-jest** (23.6.0)
- **babel-plugin-dynamic-import-node** (2.3.3)
- **chalk** (2.4.2)
- **chokidar** (2.1.5)
- **connect** (3.6.6)
- **eslint** (6.7.2)
- **eslint-plugin-vue** (6.2.2)
- **html-webpack-plugin** (3.2.0)
- **husky** (1.3.1)
- **lint-staged** (8.1.5)
- **mockjs** (1.0.1-beta3)
- **plop** (2.3.0)
- **runjs** (4.3.2)
- **sass** (1.26.2)
- **sass-loader** (8.0.2)
- **script-ext-html-webpack-plugin** (2.1.3)
- **serve-static** (1.13.2)
- **svg-sprite-loader** (4.1.3)
- **svgo** (1.2.0)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### ✏️ 编辑器组件

##### 📋 其他 编辑器组件依赖

以下 2 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **codemirror** (5.45.0)
- **tui-editor** (1.3.3)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 📊 图表组件

##### 📋 其他 图表组件依赖

以下 1 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **echarts** (4.2.1)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 🎨 UI 组件库

##### 📋 其他 UI 组件库依赖

以下 1 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **element-ui** (2.13.2)

**建议操作**:
1. 查找该组件库的 Vue 3 版本
2. 如果没有 Vue 3 版本，寻找替代方案
3. 更新组件的导入和使用方式


#### 🛠️ 工具组件

##### vue-count-to

- **当前版本**: 1.0.13
- **迁移文档**: ✅ 有
- **使用位置**: 0 处

**迁移指导**:

---
source: vue-count-to
target: vue3-count-to
link: https://github.com/xiaofan9/vue-count-to
---

# 从 vue-count-to 迁移到 vue3-count-to

`vue3-count-to` 是 `vue-count-to` 针对 Vue 3 的升级版本，旨在提供一个平滑的迁移路径。它保留了原有的大部分 API，使得迁移过程非常简单。

## 主要区别

- **Vue 3 支持**: `vue3-count-to` 完全兼容 Vue 3。
- **API 兼容性**: 大部分 `vue-count-to` 的属性和方法在 `vue3-count-to` 中都得到了保留。

## 安装

首先，你需要卸载 `vue-count-to` 并安装 `vue3-count-to`：

```bash
npm uninstall vue-count-to
npm install vue3-count-to
```

或者使用 yarn:

```bash
yarn remove vue-count-to
yarn add vue3-count-to
```

## 使用方法

`vue3-count-to` 的使用方式与 `vue-count-to` 非常相似。

### 1. 注册组件

你可以选择全局注册或局部注册。

#### 全局注册 (在 `main.js` 中)

```javascript
import { createApp } from 'vue'
import App from './App.vue'
import countTo from 'vue3-count-to'

const app = createApp(App)
app.use(countTo)
app.mount('#app')
```

#### 局部注册

```vue
<template>
  <count-to :startVal="startVal" :endVal="endVal" :duration="3000"></count-to>
</template>

<script>
import { CountTo } from 'vue3-count-to'

export default {
  components: {
    CountTo
  },
  data() {
    return {
      startVal: 0,
      endVal: 2023
    }
  }
}
</script>
```

如果使用 `<script setup>`:

```vue
<template>
  <count-to :start-val="0" :end-val="2023" :duration="3000" />
</template>

<script setup>
import { CountTo } from 'vue3-count-to';
</script>
```

### 2. 模板迁移

基本上，你只需要将 `vue-count-to` 的组件标签 `count-to` 或 `countTo` 替换为 `vue3-count-to` 的相应用法即可。由于 API 兼容，大部分属性绑定都可以直接保留。

**Vue 2 with `vue-count-to`:**
```html
<template>
  <countTo ref="counter" :startVal='startVal' :endVal='endVal' :duration='3000' @mountedCallback="onMounted"></countTo>
</template>
```

**Vue 3 with `vue3-count-to`:**
```vue
<template>
  <count-to ref="counter" :start-val="startVal" :end-val="endVal" :duration="3000" @mounted="onMounted"></count-to>
</template>

<script setup>
import { ref } from 'vue'

const startVal = ref(0)
const endVal = ref(2023)

const onMounted = () => {
  console.log('Component has been mounted!')
}
</script>
```
**注意**: 在 Vue 3 中，事件名建议使用 kebab-case 格式，例如 `mountedCallback` 变为 `mounted`。你需要查阅 `vue3-count-to` 的文档以确认具体的事件名。

## 属性和方法

`vue3-count-to` 保留了 `vue-count-to` 的大部分属性和方法。你可以继续使用 `startVal` (或 `start-val`), `endVal` (或 `end-val`), `duration`, `autoplay`, `decimals`, `separator`, `prefix`, `suffix` 等属性。

控制动画的方法如 `start()`, `pause()`, `reset()` 也可以通过 `ref` 调用。

### 通过 ref 控制

```vue
<template>
  <div>
    <count-to ref="counterRef" :end-val="1000" :autoplay="false" />
    <button @click="startCount">Start</button>
    <button @click="pauseCount">Pause</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { CountTo } from 'vue3-count-to'

const counterRef = ref(null)

const startCount = () => {
  counterRef.value?.start()
}

const pauseCount = () => {
  counterRef.value?.pause()
}
</script>
```

## 总结

从 `vue-count-to` 迁移到 `vue3-count-to` 是一个相对直接的过程。主要工作是更新依赖包和调整组件的导入和注册方式。由于 API 的高度兼容性，大部分现有的模板和逻辑都可以不做修改或只需少量修改即可正常工作。

更多详情请参考 [vue3-count-to 的 npm 页面](https://www.npmjs.com/package/vue3-count-to) 和其引用的 [vue-count-to 文档](https://github.com/PanJiaChen/vue-countTo)。 

---

##### vue-splitpane

- **当前版本**: 1.0.4
- **迁移文档**: ✅ 有
- **使用位置**: 0 处

**迁移指导**:

---
source: vue-splitpane
target: splitpanes
link: https://github.com/antoniandre/splitpanes
---

# 从 vue-splitpane 迁移到 splitpanes

`splitpanes` 是一个功能强大、可靠、简洁且支持触摸操作的 Vue.js 窗格分割/调整器，兼容 Vue 3 和 Vue 2。本文档将指导你如何从 `vue-splitpane` 迁移到 `splitpanes`。

## 主要区别

- **组件化**: `splitpanes` 采用更现代的组件化 API，使用 `<splitpanes>` 作为容器，`<pane>` 作为独立的窗格组件。
- **功能更丰富**: `splitpanes` 提供了更多功能，如水平/垂直分割、RTL 支持、事件监听、窗格最小/最大尺寸限制等。
- **Vue 3 支持**: `splitpanes` 为 Vue 3 提供了原生支持。

## 安装

首先，你需要从你的项目中移除 `vue-splitpane`，然后安装 `splitpanes`。

```bash
npm uninstall vue-splitpane
npm install splitpanes
```

或者使用 yarn:

```bash
yarn remove vue-splitpane
yarn add splitpanes
```

## 使用方法

### 1. 导入组件和样式

在你的 Vue 组件中，你需要导入 `Splitpanes` 和 `Pane` 组件，以及它的 CSS 文件。

```vue
<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 2. 基本用法

`vue-splitpane` 通常像下面这样使用：

```html
<!-- vue-splitpane 示例 -->
<split-pane :min-percent='20' :default-percent='30' split="vertical">
  <template slot="paneL">A</template>
  <template slot="paneR">B</template>
</split-pane>
```

使用 `splitpanes`，你可以这样实现：

```vue
<template>
  <splitpanes style="height: 400px">
    <pane min-size="20" size="30">
      <span>A</span>
    </pane>
    <pane>
      <span>B</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 3. 水平分割

在 `splitpanes` 中，你可以使用 `horizontal` 属性来实现水平分割：

```vue
<template>
  <splitpanes horizontal style="height: 400px">
    <pane min-size="20">
      <span>A</span>
    </pane>
    <pane>
      <span>B</span>
    </pane>
    <pane>
      <span>C</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

### 4. 嵌套使用

`splitpanes` 支持嵌套，可以创建复杂的布局：

```vue
<template>
  <splitpanes style="height: 400px">
    <pane>
      <span>1</span>
    </pane>
    <pane>
      <splitpanes horizontal>
        <pane>
          <span>2</span>
        </pane>
        <pane>
          <span>3</span>
        </pane>
        <pane>
          <span>4</span>
        </pane>
      </splitpanes>
    </pane>
    <pane>
      <span>5</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'
</script>
```

## 属性（Props）

### Splitpanes 组件

- `horizontal`: `Boolean`, 默认为 `false`。设置为 `true` 时，窗格将水平排列。
- `push-other-panes`: `Boolean`, 默认为 `true`。当一个窗格调整大小时，是否会推挤其他窗格。
- `rtl`: `Boolean`, 默认为 `false`。为从右到左的语言提供支持。
- `first-splitter`: `Boolean`, 默认为 `false`。在第一个窗格前添加一个分隔条。

### Pane 组件

- `size`: `Number`, 默认为 `null`。设置窗格的初始大小，单位是百分比。剩余空间将平均分配给未设置大小的窗格。
- `min-size`: `Number`, 默认为 `0`。窗格的最小尺寸。
- `max-size`: `Number`, 默认为 `100`。窗格的最大尺寸。

## 事件（Events）

`splitpanes` 提供了一系列事件来监听窗格的变化：

- `ready`: 在 `splitpanes` 初始化并且所有窗格都挂载后触发。
- `resize`: 在调整任何窗格大小时触发，参数为包含每个窗格大小的数组。
- `pane-click`: 点击窗格时触发，参数为被点击的窗格对象。
- `pane-maximize`: 当窗格最大化或恢复时触发。
- `splitter-click`: 当点击分隔条时触发。

### 事件使用示例

```vue
<template>
  <splitpanes @resize="onResize" style="height: 400px">
    <pane v-for="i in 3" :key="i">
      <span>{{ i }}</span>
    </pane>
  </splitpanes>
</template>

<script setup>
import { Splitpanes, Pane } from 'splitpanes'
import 'splitpanes/dist/splitpanes.css'

const onResize = (event) => {
  console.log(event.map(({ size }) => size))
}
</script>
```

## 总结

`splitpanes` 是一个现代化且功能丰富的 `vue-splitpane` 替代品。通过本文档的指引，你可以轻松地完成迁移，并利用其更多高级功能来构建灵活的页面布局。更多详细信息和示例，请参考[官方文档](https://antoniandre.github.io/splitpanes/)。 

---

##### vue-template-compiler

- **当前版本**: 2.6.10
- **迁移文档**: ✅ 有
- **使用位置**: 0 处

**迁移指导**:

---
title: 迁移 vue-template-compiler
---

# 迁移指南: `vue-template-compiler` 到 `@vue/compiler-sfc`

本指南解释了从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的迁移。与其他关注 UI 组件的指南不同，本指南处理的是 Vue 构建过程的核心部分。对于许多使用标准工具（如 Vue CLI 或 Vite）的开发者来说，此更改将在项目升级过程中自动处理。

然而，如果你有一个自定义的构建设置（例如，一个自定义的 Webpack 配置），理解这个变化是至关重要的。

## 编译器的角色

-   在 **Vue 2** 中，`vue-template-compiler` 是一个用于将 `.vue` 文件中的 `<template>` 块编译成 JavaScript `render` 函数的包。它的版本必须与核心 `vue` 包保持同步。

-   在 **Vue 3** 中，这个职责被分开了。新的 `@vue/compiler-sfc` 包负责解析单文件组件（SFCs），而实际的模板编译则由 `@vue/compiler-dom` 处理，它现在是 `vue` 包本身的核心依赖项。

从本质上讲，`@vue/compiler-sfc` 是构建工具用来理解和处理 `.vue` 文件的工具。

---

## 分步迁移指南

### 1. 依赖更新

第一步是调整你项目的 `devDependencies`。

1.  **移除 `vue-template-compiler`**: 在 Vue 3 项目中不再需要这个包。

    ```bash
    npm uninstall vue-template-compiler
    ```

2.  **检查 `@vue/compiler-sfc`**: 这个包是现代 Vue 构建插件（Webpack 的 `vue-loader`，Vite 的 `@vitejs/plugin-vue`）的对等依赖（peer dependency）。在大多数情况下，你 **不** 需要手动安装它。它会随着构建插件一起被安装。如果由于特定原因需要添加它，它应该是一个 `devDependency`。

    ```bash
    # 仅在你的特定设置需要时
    npm install -D @vue/compiler-sfc
    ```

### 2. 构建配置更新

这是最可能发生重大手动更改的地方，具体取决于你的构建工具。

#### 对于 Vue CLI 用户

当你使用官方迁移命令（`vue upgrade`）将项目升级到 Vue 3 时，Vue CLI 会自动更新你的 Webpack 配置、`vue-loader` 以及所有相关依赖。这个变更会自动为你处理。

#### 对于 Vite 用户

如果你正在使用 Vite 创建一个新的 Vue 3 项目或迁移现有项目，`@vitejs/plugin-vue` 插件会处理 SFC 编译。它在底层使用 `@vue/compiler-sfc`，你无需进行任何手动配置。

#### 对于自定义 Webpack 用户

如果你管理自己的 Webpack 配置，你需要进行以下更改：

1.  **更新 `vue-loader`**: 你必须将 `vue-loader` 更新到版本 16 或更高。版本 16 是为 Vue 3 设计的，并使用 `@vue/compiler-sfc`。

    ```bash
    npm install -D vue-loader@^16.0.0
    ```

2.  **更新 Webpack 配置**: `vue-loader` 插件的导入方式已更改。

    **Vue 2 `webpack.config.js`:**
    ```javascript
    const { VueLoaderPlugin } = require('vue-loader'); // 适用于 v15 及以下版本

    module.exports = {
      // ...
      plugins: [
        new VueLoaderPlugin(),
      ],
    };
    ```

    **Vue 3 `webpack.config.js`:**
    ```javascript
    const { VueLoaderPlugin } = require('vue-loader'); // 适用于 v16+

    module.exports = {
      // ...
      plugins: [
        new VueLoaderPlugin(),
      ],
    };
    ```
    虽然 import 语句可能看起来一样，但请确保你已经更新了 `vue-loader` 包的版本。新版本的加载器将自动使用 `@vue/compiler-sfc`，并且不需要 `vue-template-compiler`。

## 总结

从 `vue-template-compiler` 到 `@vue/compiler-sfc` 的过渡是 Vue 3 构建过程中的一个根本性转变。

-   如果你使用像 **Vue CLI** 或 **Vite** 这样的标准项目设置，这个迁移在很大程度上是 **自动的**。
-   如果你有一个 **自定义的 Webpack 设置**，关键是升级 `vue-loader` 到 v16+ 并移除现在已经过时的 `vue-template-compiler`。

通过确保你的构建工具为 Vue 3 正确配置，新的编译器将在幕后无缝工作。 

---

##### 📋 其他 工具组件依赖

以下 5 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **@vue/cli-plugin-babel** (4.4.4)
- **@vue/cli-plugin-eslint** (4.4.4)
- **@vue/cli-plugin-unit-jest** (4.4.4)
- **@vue/cli-service** (4.4.4)
- **@vue/test-utils** (1.0.0-beta.29)

**建议操作**:
1. 访问 [undefined 官方文档](https://www.npmjs.com/package/undefined)
2. 检查是否支持 Vue 3
3. 如不支持，寻找 Vue 3 兼容的替代方案


#### 🔧 Vue 核心

##### 📋 其他 Vue 核心依赖

以下 2 个依赖暂无专门的迁移文档，请参考通用迁移建议：

- **vue-router** (3.0.2)
- **vuex** (3.1.0)

**建议操作**:
1. 检查 [Vue 3 官方迁移指南](https://v3-migration.vuejs.org/)
2. 更新到 Vue 3 兼容版本
3. 使用 Vue 3 迁移构建版本进行渐进式迁移




## 💻 代码迁移指南

### 通用迁移原则

1. **渐进式迁移**: 建议使用 Vue 3 的兼容构建版本进行渐进式迁移
2. **组件优先**: 先迁移叶子组件，再迁移父组件
3. **测试驱动**: 每迁移一个组件都要进行充分测试

### 主要变更点

#### 1. 组件定义方式
```javascript
// Vue 2
export default {
  name: 'MyComponent',
  // ...
}

// Vue 3 (推荐)
import { defineComponent } from 'vue'
export default defineComponent({
  name: 'MyComponent',
  // ...
})
```

#### 2. 生命周期钩子
```javascript
// Vue 2
beforeDestroy() { },
destroyed() { }

// Vue 3
beforeUnmount() { },
unmounted() { }
```

#### 3. 事件处理
```javascript
// Vue 2
this.$on('event', handler)
this.$off('event', handler)
this.$emit('event', data)

// Vue 3
// 使用 mitt 或其他事件库替代 $on/$off
this.$emit('event', data) // 仍然可用
```

### 文件修改建议

暂未发现需要特别关注的文件。

## 🚀 分步迁移指南

### 阶段一：准备工作 (1-2 天)

1. **项目备份**
   ```bash
   git checkout -b vue3-migration
   git commit -am "开始 Vue 3 迁移"
   ```

2. **依赖分析**
   - 使用本工具分析项目依赖
   - 制定详细的迁移计划
   - 准备测试环境

### 阶段二：核心依赖迁移 (2-3 天)

1. **Vue 核心升级**
   ```bash
   npm install vue@next @vue/compat
   npm install vue-router@4 vuex@4  # 或 pinia
   ```

2. **构建工具配置**
   - 更新 webpack/vite 配置
   - 配置 Vue 3 兼容模式

### 阶段三：组件库迁移 (3-5 天)

1. **UI 组件库**
   - Element UI → Element Plus
   - 其他 UI 库的 Vue 3 版本

2. **自定义组件**
   - 逐个迁移自定义组件
   - 更新组件的使用方式

### 阶段四：业务代码迁移 (5-10 天)

1. **页面组件迁移**
   - 从叶子组件开始
   - 逐步向上迁移

2. **状态管理迁移**
   - Vuex 4 或迁移到 Pinia
   - 更新状态管理逻辑

### 阶段五：测试和优化 (2-3 天)

1. **功能测试**
   - 全面测试所有功能
   - 修复发现的问题

2. **性能优化**
   - 利用 Vue 3 的性能优势
   - 优化组件结构

## 🔧 常见问题

### 构建错误

**问题**: `Module not found: Error: Can't resolve 'vue'`
**解决**: 确保安装了正确版本的 Vue 3

**问题**: `TypeError: Cannot read property '$on' of undefined`
**解决**: Vue 3 移除了 $on/$off，使用事件总线库如 mitt

### 组件问题

**问题**: Element UI 组件不工作
**解决**: 迁移到 Element Plus 并更新组件名称

**问题**: 自定义组件报错
**解决**: 检查生命周期钩子名称变更

### 性能问题

**问题**: 页面渲染变慢
**解决**: 检查是否正确使用了 Vue 3 的响应式 API

## 📚 参考资源

### 官方文档

- [Vue 3 官方文档](https://v3.vuejs.org/)
- [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
- [Vue Router 4](https://next.router.vuejs.org/)
- [Vuex 4](https://next.vuex.vuejs.org/) / [Pinia](https://pinia.vuejs.org/)

### 组件库

- [Element Plus](https://element-plus.org/)
- [Ant Design Vue 3](https://antdv.com/)
- [Vuetify 3](https://vuetifyjs.com/)

### 工具和插件

- [Vue DevTools](https://devtools.vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Vue CLI](https://cli.vuejs.org/)

### 社区资源

- [Vue 3 生态系统](https://github.com/vuejs/awesome-vue)
- [Vue 3 迁移案例](https://github.com/topics/vue3-migration)

---

*本文档由 vue-migrator 工具自动生成。如有问题，请参考官方文档或寻求社区帮助。*

**祝您迁移顺利！** 🎉